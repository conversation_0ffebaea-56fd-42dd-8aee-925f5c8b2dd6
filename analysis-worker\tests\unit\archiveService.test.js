/**
 * Unit tests for Archive Service
 */

const axios = require('axios');

// Mock dependencies
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    post: jest.fn(),
    put: jest.fn(),
    get: jest.fn(),
    interceptors: {
      request: {
        use: jest.fn()
      },
      response: {
        use: jest.fn()
      }
    }
  }))
}));
jest.mock('../../src/utils/logger');

const archiveService = require('../../src/services/archiveService');

describe('Archive Service', () => {
  let mockAxiosInstance;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Get the mocked axios instance
    mockAxiosInstance = axios.create();
  });

  describe('saveAnalysisResult', () => {
    const mockUserId = 'test-user-id';
    const mockAssessmentData = {
      riasec: { realistic: 75 },
      ocean: { openness: 80 }
    };
    const mockPersonaProfile = [
      {
        archetype: "Test Archetype",
        shortSummary: "Test summary",
        strengths: ["Strength 1"],
        weakness: ["Weakness 1"],
        careerRecommendation: ["Career 1"],
        insights: ["Insight 1"],
        workEnvironment: "Test environment",
        roleModel: ["Role Model 1"]
      }
    ];
    const mockJobId = 'test-job-id';

    it('should save analysis result successfully', async () => {
      // Arrange
      const mockResponse = {
        data: {
          success: true,
          message: 'Analysis result saved successfully',
          data: {
            id: 'test-result-id',
            status: 'completed',
            created_at: '2024-01-01T00:00:00.000Z'
          }
        },
        status: 201
      };
      
      mockAxiosInstance.post.mockResolvedValue(mockResponse);
      
      // Act
      const result = await archiveService.saveAnalysisResult(
        mockUserId,
        mockAssessmentData,
        mockPersonaProfile,
        mockJobId
      );
      
      // Assert
      expect(result).toEqual({
        success: true,
        id: 'test-result-id',
        status: 'completed',
        created_at: '2024-01-01T00:00:00.000Z'
      });
      
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/archive/results',
        {
          user_id: mockUserId,
          assessment_data: mockAssessmentData,
          persona_profile: mockPersonaProfile,
          status: 'completed'
        }
      );
    });

    it('should throw error if Archive service fails', async () => {
      // Arrange
      const mockError = {
        message: 'Archive service error',
        response: {
          status: 500,
          statusText: 'Internal Server Error'
        }
      };
      
      mockAxiosInstance.post.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(archiveService.saveAnalysisResult(
        mockUserId,
        mockAssessmentData,
        mockPersonaProfile,
        mockJobId
      )).rejects.toMatchObject({
        message: 'Archive service error',
        isRetryable: true
      });
    });

    it('should mark 4xx errors as non-retryable', async () => {
      // Arrange
      const mockError = {
        message: 'Bad request',
        response: {
          status: 400,
          statusText: 'Bad Request'
        }
      };
      
      axios.post.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(archiveService.saveAnalysisResult(
        mockUserId,
        mockAssessmentData,
        mockPersonaProfile,
        mockJobId
      )).rejects.toMatchObject({
        message: 'Bad request',
        isRetryable: false
      });
    });

    it('should mark network errors as retryable', async () => {
      // Arrange
      const mockError = {
        message: 'Network error',
        code: 'ECONNREFUSED'
      };
      
      axios.post.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(archiveService.saveAnalysisResult(
        mockUserId,
        mockAssessmentData,
        mockPersonaProfile,
        mockJobId
      )).rejects.toMatchObject({
        message: 'Network error',
        isRetryable: true
      });
    });
  });

  describe('updateAnalysisResult', () => {
    const mockResultId = 'test-result-id';
    const mockStatus = 'completed';
    const mockJobId = 'test-job-id';

    it('should update analysis result status successfully', async () => {
      // Arrange
      const mockResponse = {
        data: {
          success: true,
          message: 'Analysis result updated successfully',
          data: {
            id: mockResultId,
            updated_at: '2024-01-01T01:00:00.000Z'
          }
        },
        status: 200
      };
      
      axios.put.mockResolvedValue(mockResponse);
      
      // Act
      const result = await archiveService.updateAnalysisResult(
        mockResultId,
        mockStatus,
        mockJobId
      );
      
      // Assert
      expect(result).toEqual({
        success: true,
        id: mockResultId,
        updated_at: '2024-01-01T01:00:00.000Z'
      });
      
      expect(axios.put).toHaveBeenCalledWith(
        `/archive/results/${mockResultId}`,
        { status: mockStatus }
      );
    });

    it('should throw error if update fails', async () => {
      // Arrange
      const mockError = {
        message: 'Update failed',
        response: {
          status: 500,
          statusText: 'Internal Server Error'
        }
      };
      
      axios.put.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(archiveService.updateAnalysisResult(
        mockResultId,
        mockStatus,
        mockJobId
      )).rejects.toMatchObject({
        message: 'Update failed',
        isRetryable: true
      });
    });
  });

  describe('checkHealth', () => {
    it('should return true if Archive service is healthy', async () => {
      // Arrange
      const mockResponse = {
        status: 200,
        data: {
          status: 'healthy'
        }
      };
      
      axios.get.mockResolvedValue(mockResponse);
      
      // Act
      const result = await archiveService.checkHealth();
      
      // Assert
      expect(result).toBe(true);
      expect(axios.get).toHaveBeenCalledWith('/health', { timeout: 5000 });
    });

    it('should return false if Archive service is unhealthy', async () => {
      // Arrange
      axios.get.mockRejectedValue(new Error('Connection refused'));
      
      // Act
      const result = await archiveService.checkHealth();
      
      // Assert
      expect(result).toBe(false);
    });
  });
});
