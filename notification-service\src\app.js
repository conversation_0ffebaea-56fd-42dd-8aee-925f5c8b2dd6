const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const morgan = require('morgan');
require('dotenv').config();

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const connectionManager = require('./utils/connectionManager');
const healthRoutes = require('./routes/health');
const notificationRoutes = require('./routes/notifications');
const QueueConsumer = require('./services/queueConsumer');
const NotificationService = require('./services/notificationService');

// Create Express app
const app = express();
const server = http.createServer(app);

// Socket.IO setup
const io = socketIo(server, {
  cors: {
    origin: process.env.WEBSOCKET_CORS_ORIGIN ? 
      process.env.WEBSOCKET_CORS_ORIGIN.split(',') : 
      ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST']
  },
  pingTimeout: parseInt(process.env.WEBSOCKET_PING_TIMEOUT || '60000'),
  pingInterval: parseInt(process.env.WEBSOCKET_PING_INTERVAL || '25000')
});

const PORT = process.env.PORT || 3005;

// CORS configuration for HTTP routes
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? 
    process.env.CORS_ORIGIN.split(',') : 
    ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Request ID middleware
app.use((req, res, next) => {
  req.id = require('crypto').randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// WebSocket Authentication Middleware
io.use((socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      logger.warn('WebSocket authentication failed: No token provided', {
        socketId: socket.id,
        ip: socket.handshake.address
      });
      return next(new Error('Authentication token required'));
    }

    // Verify JWT token
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Attach user info to socket
    socket.userId = decoded.id;
    socket.userEmail = decoded.email;
    
    logger.debug('WebSocket authentication successful', {
      socketId: socket.id,
      userId: decoded.id,
      email: decoded.email,
      ip: socket.handshake.address
    });
    
    next();
    
  } catch (error) {
    logger.warn('WebSocket authentication failed', {
      socketId: socket.id,
      error: error.message,
      ip: socket.handshake.address
    });
    
    next(new Error('Authentication failed'));
  }
});

// WebSocket Connection Handling
io.on('connection', (socket) => {
  logger.info('WebSocket connection established', {
    socketId: socket.id,
    userId: socket.userId,
    email: socket.userEmail,
    ip: socket.handshake.address,
    userAgent: socket.handshake.headers['user-agent']
  });

  // Add connection to manager
  connectionManager.addConnection(socket.userId, socket);

  // Send welcome message
  socket.emit('connected', {
    message: 'Connected to notification service',
    timestamp: new Date().toISOString(),
    socketId: socket.id
  });

  // Handle ping for keeping connection alive
  socket.on('ping', () => {
    connectionManager.updateActivity(socket.id);
    socket.emit('pong', { timestamp: new Date().toISOString() });
  });

  // Handle custom events
  socket.on('subscribe', (data) => {
    logger.debug('User subscribed to notifications', {
      socketId: socket.id,
      userId: socket.userId,
      data
    });
    connectionManager.updateActivity(socket.id);
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    logger.info('WebSocket connection disconnected', {
      socketId: socket.id,
      userId: socket.userId,
      reason,
      ip: socket.handshake.address
    });

    // Remove connection from manager
    connectionManager.removeConnection(socket.id);
  });

  // Handle connection errors
  socket.on('error', (error) => {
    logger.error('WebSocket connection error', {
      socketId: socket.id,
      userId: socket.userId,
      error: error.message,
      ip: socket.handshake.address
    });
  });
});

// Make io available to other modules
app.set('io', io);

// Initialize notification service
const notificationService = new NotificationService(io);
app.set('notificationService', notificationService);

// Initialize and start queue consumer
let queueConsumer = null;
const initializeQueueConsumer = async () => {
  try {
    queueConsumer = new QueueConsumer(io);
    const started = await queueConsumer.start();

    if (started) {
      logger.info('Queue consumer initialized successfully');
    } else {
      logger.error('Failed to initialize queue consumer');
    }
  } catch (error) {
    logger.error('Error initializing queue consumer', {
      error: error.message,
      stack: error.stack
    });
  }
};

// Start queue consumer after a short delay to ensure everything is ready
setTimeout(initializeQueueConsumer, 2000);

// Routes
app.use('/health', healthRoutes);
app.use('/notifications', notificationRoutes);

// Root endpoint
app.get('/', (req, res) => {
  const stats = connectionManager.getStats();
  
  res.json({
    success: true,
    message: 'ATMA Notification Service is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    websocket: {
      connectedUsers: stats.totalUsers,
      totalConnections: stats.totalConnections
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    }
  });
});

// Error handling middleware
app.use(errorHandler);

// Cleanup inactive connections periodically
setInterval(() => {
  connectionManager.cleanupInactiveConnections();
}, parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'));

// Start server
const serverInstance = server.listen(PORT, () => {
  logger.info(`Notification Service started on port ${PORT}`, {
    port: PORT,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    websocketCors: process.env.WEBSOCKET_CORS_ORIGIN,
    httpCors: process.env.CORS_ORIGIN
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);

  // Stop queue consumer
  if (queueConsumer) {
    try {
      await queueConsumer.stop();
      logger.info('Queue consumer stopped');
    } catch (error) {
      logger.error('Error stopping queue consumer', { error: error.message });
    }
  }

  // Close server
  serverInstance.close(() => {
    logger.info('HTTP server closed');

    // Close all WebSocket connections
    io.close(() => {
      logger.info('WebSocket server closed');

      // Exit process
      logger.info('Process terminated');
      process.exit(0);
    });
  });

  // Force close after 10 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason: reason,
    promise: promise
  });
  process.exit(1);
});

module.exports = { app, server, io };
