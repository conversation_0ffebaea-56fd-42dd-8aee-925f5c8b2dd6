[{"name": "checkstyle", "description": "Outputs results to the [Checkstyle](https://checkstyle.sourceforge.io/) format."}, {"name": "compact", "description": "Human-readable output format. Mimics the default output of JSHint."}, {"name": "html", "description": "Outputs results to HTML. The `html` formatter is useful for visual presentation in the browser."}, {"name": "jslint-xml", "description": "Outputs results to format compatible with the [JSLint Jenkins plugin](https://plugins.jenkins.io/jslint/)."}, {"name": "json-with-metadata", "description": "Outputs JSON-serialized results. The `json-with-metadata` provides the same linting results as the [`json`](#json) formatter with additional metadata about the rules applied. The linting results are included in the `results` property and the rules metadata is included in the `metadata` property.\n\nAlternatively, you can use the [ESLint Node.js API](../../integrate/nodejs-api) to programmatically use ESLint."}, {"name": "json", "description": "Outputs JSON-serialized results. The `json` formatter is useful when you want to programmatically work with the CLI's linting results.\n\nAlternatively, you can use the [ESLint Node.js API](../../integrate/nodejs-api) to programmatically use ESLint."}, {"name": "junit", "description": "Outputs results to format compatible with the [JUnit Jenkins plugin](https://plugins.jenkins.io/junit/)."}, {"name": "stylish", "description": "Human-readable output format. This is the default formatter."}, {"name": "tap", "description": "Outputs results to the [Test Anything Protocol (TAP)](https://testanything.org/) specification format."}, {"name": "unix", "description": "Outputs results to a format similar to many commands in UNIX-like systems. Parsable with tools such as [grep](https://www.gnu.org/software/grep/manual/grep.html), [sed](https://www.gnu.org/software/sed/manual/sed.html), and [awk](https://www.gnu.org/software/gawk/manual/gawk.html)."}, {"name": "visualstudio", "description": "Outputs results to format compatible with the integrated terminal of the [Visual Studio](https://visualstudio.microsoft.com/) IDE. When using Visual Studio, you can click on the linting results in the integrated terminal to go to the issue in the source code."}]